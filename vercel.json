{"version": 2, "name": "epos-service-ui-form", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"app/api/payment/create/route.ts": {"maxDuration": 30}, "app/api/payment/callback/route.ts": {"maxDuration": 30}, "app/api/payment/status/[paymentId]/route.ts": {"maxDuration": 30}, "app/api/health/route.ts": {"maxDuration": 10}}}