#!/usr/bin/env node

/**
 * Deployment helper script for Vercel
 * This script helps set up environment variables and deploy to Vercel
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 EPOS Service UI Form - Vercel Deployment Helper\n');

// Check if vercel CLI is installed
try {
  execSync('vercel --version', { stdio: 'ignore' });
} catch (error) {
  console.log('❌ Vercel CLI not found. Installing...');
  execSync('npm install -g vercel', { stdio: 'inherit' });
}

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  .env.local not found. Please create it with your environment variables.');
  console.log('   Copy .env.example to .env.local and fill in your values.\n');
}

console.log('📋 Deployment Checklist:');
console.log('   ✅ Vercel CLI installed');
console.log('   ✅ Project configured for Next.js');
console.log('   ✅ API routes ready');
console.log('   ✅ Environment variables template available');

console.log('\n🔧 Next steps:');
console.log('   1. Run: vercel login');
console.log('   2. Run: vercel');
console.log('   3. Set environment variables in Vercel dashboard:');
console.log('      - GTXPOINT_PATH');
console.log('      - GTXPOINT_PROJECT_ID');
console.log('      - GTXPOINT_SECRET_KEY');

console.log('\n🌐 Your app will be available at: https://your-app-name.vercel.app');
console.log('\n✨ Happy deploying!');
