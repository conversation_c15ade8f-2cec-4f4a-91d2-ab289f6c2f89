import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const testData = {
      message: 'EPOS Service UI API is working!',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      endpoints: {
        health: '/api/health',
        paymentCreate: '/api/payment/create',
        paymentCallback: '/api/payment/callback',
        paymentStatus: '/api/payment/status/[paymentId]'
      },
      gtxpoint: {
        configured: !!(process.env.GTXPOINT_PATH && process.env.GTXPOINT_PROJECT_ID && process.env.GTXPOINT_SECRET_KEY),
        path: process.env.GTXPOINT_PATH || 'https://paymentpage.eposservice.com',
        projectId: process.env.GTXPOINT_PROJECT_ID || '143693'
      }
    };

    return NextResponse.json(testData);
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Test endpoint failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
