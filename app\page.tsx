'use client';

import { useState } from 'react';
import PaymentConfigForm from '@/components/PaymentConfigForm';
import PaymentIframe from '@/components/PaymentIframe';

export default function HomePage() {
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handlePaymentUrlGenerated = (url: string) => {
    setPaymentUrl(url);
  };

  const handleLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50/30">
      {/* Header */}
      <header className="relative bg-white/80 backdrop-blur-md border-b border-neutral-200/50 shadow-soft">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-secondary-500/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-medium">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-700 bg-clip-text text-transparent">
                  EPOS Service UI
                </h1>
                <p className="text-neutral-600 text-sm font-medium">Premium Payment Processing Interface</p>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-3 px-4 py-2 bg-success-50 border border-success-200 rounded-xl">
                <div className="relative">
                  <div className="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-3 h-3 bg-success-500 rounded-full animate-ping opacity-75"></div>
                </div>
                <span className="text-sm font-medium text-success-700">GTXPoint Connected</span>
              </div>
              <div className="hidden sm:flex items-center gap-2 text-sm text-neutral-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>Secure & Encrypted</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-400/10 to-secondary-400/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-secondary-400/10 to-accent-400/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-12 min-h-[calc(100vh-200px)]">
          {/* Left Column - Payment Form */}
          <div className="space-y-8">
            <div className="animate-fade-in-up">
              <PaymentConfigForm
                onPaymentUrlGenerated={handlePaymentUrlGenerated}
                onLoading={handleLoading}
              />
            </div>

            {/* Quick Info Card */}
            <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <div className="relative overflow-hidden bg-gradient-to-br from-primary-50 to-secondary-50 border border-primary-200/50 rounded-2xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 group">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-medium">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-neutral-900">
                      How it works
                    </h3>
                  </div>
                  <div className="space-y-3">
                    {[
                      'Configure payment details in the form',
                      'Generate secure payment URL instantly',
                      'Preview payment page in real-time',
                      'Copy URL or open in new tab for testing'
                    ].map((step, index) => (
                      <div key={index} className="flex items-center gap-3 group/item">
                        <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center text-xs font-semibold text-primary-600 shadow-soft group-hover/item:shadow-medium transition-all duration-200">
                          {index + 1}
                        </div>
                        <p className="text-neutral-700 text-sm font-medium">{step}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Payment Iframe */}
          <div className="lg:sticky lg:top-8 lg:h-fit animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            <PaymentIframe
              paymentUrl={paymentUrl}
              isLoading={isLoading}
            />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="relative bg-white/80 backdrop-blur-md border-t border-neutral-200/50 mt-20">
        <div className="absolute inset-0 bg-gradient-to-r from-neutral-50/50 to-primary-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <p className="text-neutral-700 text-sm font-medium">
                  © 2025 EPOS Service UI
                </p>
                <p className="text-neutral-500 text-xs">
                  Powered by GTXPoint Payment Gateway
                </p>
              </div>
            </div>

            <div className="flex items-center gap-6">
              <a
                href="/api/health"
                target="_blank"
                className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-primary-600 bg-neutral-100 hover:bg-primary-50 rounded-lg transition-all duration-200 hover:shadow-soft"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                API Health
              </a>

              <a
                href="/api/test"
                target="_blank"
                className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-secondary-600 bg-neutral-100 hover:bg-secondary-50 rounded-lg transition-all duration-200 hover:shadow-soft"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                API Test
              </a>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-neutral-200/50">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-xs text-neutral-500">
              <div className="flex items-center gap-4">
                <span>Built with Next.js 15 & TypeScript</span>
                <span>•</span>
                <span>Styled with Tailwind CSS</span>
                <span>•</span>
                <span>Secured by GTXPoint</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                <span>All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
