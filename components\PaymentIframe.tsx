'use client';

import { useState } from 'react';
import Card, { <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';

interface PaymentIframeProps {
  paymentUrl: string | null;
  isLoading: boolean;
}

export default function PaymentIframe({ paymentUrl, isLoading }: PaymentIframeProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [iframeLoading, setIframeLoading] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopyUrl = async () => {
    if (!paymentUrl) return;
    
    try {
      await navigator.clipboard.writeText(paymentUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  const handleOpenInNewTab = () => {
    if (paymentUrl) {
      window.open(paymentUrl, '_blank');
    }
  };

  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  if (!paymentUrl && !isLoading) {
    return (
      <Card variant="elevated" className="h-full animate-fade-in-up">
        <CardHeader
          title="Payment Page Preview"
          subtitle="Your generated payment page will appear here"
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          }
        />
        <CardContent>
          <div className="flex items-center justify-center h-96 bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-2xl border-2 border-dashed border-neutral-300 relative overflow-hidden group">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 animate-gradient-x"></div>
            </div>

            <div className="text-center relative z-10">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-300 group-hover:scale-105">
                <svg className="w-10 h-10 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-3">Ready to Generate Payment URL</h3>
              <p className="text-neutral-600 max-w-sm mx-auto leading-relaxed">
                Complete the payment configuration form and click "Generate Payment URL" to preview your secure payment page here.
              </p>
              <div className="mt-6 flex items-center justify-center gap-2 text-sm text-neutral-500">
                <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                <span>Waiting for configuration...</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      variant="elevated"
      padding="none"
      className={cn(
        'animate-fade-in-up transition-all duration-300',
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : 'h-full'
      )}
    >
      <CardHeader
        title="Payment Page Preview"
        subtitle={paymentUrl ? "Live payment page preview" : "Generate a payment URL to see preview"}
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        }
        action={
          paymentUrl && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyUrl}
                className={cn(
                  'transition-all duration-200',
                  copySuccess && 'bg-success-100 text-success-700 hover:bg-success-200'
                )}
                leftIcon={
                  copySuccess ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  )
                }
              >
                {copySuccess ? 'Copied!' : 'Copy'}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleOpenInNewTab}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                }
              >
                New Tab
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                leftIcon={
                  isFullscreen ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                  )
                }
              >
                {isFullscreen ? 'Exit' : 'Fullscreen'}
              </Button>
            </div>
          )
        }
      />

      {paymentUrl && (
        <div className="px-6 pb-4">
          <div className="bg-gradient-to-r from-neutral-50 to-neutral-100 rounded-xl p-4 border border-neutral-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-neutral-700">Payment URL Generated</span>
            </div>
            <p className="text-sm text-neutral-600 break-all font-mono bg-white rounded-lg p-2 border">
              {paymentUrl}
            </p>
          </div>
        </div>
      )}

      <CardContent padding="none">
        <div className={cn(
          'relative overflow-hidden',
          isFullscreen ? 'h-screen' : 'h-96 md:h-[600px]',
          !isFullscreen && 'rounded-b-2xl'
        )}>
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 backdrop-blur-sm">
              <div className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 border-4 border-primary-200 rounded-full animate-spin mx-auto"></div>
                  <div className="w-16 h-16 border-4 border-primary-600 border-t-transparent rounded-full animate-spin absolute top-0 left-1/2 -translate-x-1/2"></div>
                </div>
                <h3 className="text-lg font-semibold text-neutral-900 mb-2">Generating Payment URL</h3>
                <p className="text-neutral-600">Creating your secure payment page...</p>
                <div className="flex items-center justify-center gap-1 mt-4">
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}

          {paymentUrl && (
            <>
              {iframeLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neutral-50 to-neutral-100 backdrop-blur-sm z-10">
                  <div className="text-center">
                    <div className="relative mb-6">
                      <div className="w-16 h-16 border-4 border-success-200 rounded-full animate-spin mx-auto"></div>
                      <div className="w-16 h-16 border-4 border-success-600 border-t-transparent rounded-full animate-spin absolute top-0 left-1/2 -translate-x-1/2"></div>
                    </div>
                    <h3 className="text-lg font-semibold text-neutral-900 mb-2">Loading Payment Page</h3>
                    <p className="text-neutral-600">Preparing your secure payment interface...</p>
                    <div className="flex items-center justify-center gap-1 mt-4">
                      <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                  </div>
                </div>
              )}

              <iframe
                src={paymentUrl}
                className={cn(
                  'w-full h-full border-0 transition-opacity duration-500',
                  iframeLoading ? 'opacity-0' : 'opacity-100',
                  !isFullscreen && 'rounded-b-2xl'
                )}
                onLoad={handleIframeLoad}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
                title="Payment Page"
              />
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
