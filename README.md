# EPOS Service UI Form - Next.js

A Next.js application with a payment configuration form and iframe display for the EPOS Service payment system powered by GTXPoint.

## Features

- **Payment Configuration Form**: Configure payment amount, currency, customer ID, and optional customer details
- **Real-time Iframe Display**: View the generated payment page directly in an embedded iframe
- **Responsive Design**: Modern, mobile-friendly interface built with Tailwind CSS
- **Next.js API Routes**: Built-in API routes for payment processing (no separate backend needed)
- **Error Handling**: Comprehensive error handling and user feedback
- **URL Management**: Copy payment URLs and open in new tabs
- **Vercel Ready**: Optimized for Vercel deployment

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Frontend**: React 19, TypeScript
- **Styling**: Tailwind CSS
- **API**: Next.js API Routes
- **Payment Gateway**: GTXPoint
- **Deployment**: Vercel

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── health/        # Health check endpoint
│   │   └── payment/       # Payment-related endpoints
│   ├── globals.css        # Global styles with Tailwind
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Main page component
├── components/            # React components
│   ├── PaymentConfigForm.tsx  # Payment configuration form
│   └── PaymentIframe.tsx      # Iframe display component
├── lib/                   # Utility functions
│   └── api.ts            # API integration functions
├── types/                 # TypeScript type definitions
│   └── payment.ts        # Payment-related types
├── vercel.json           # Vercel deployment configuration
└── Configuration files...
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd epos_service_ui_form
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment configuration:
```bash
cp .env.example .env.local
```

4. Update the environment variables in `.env.local` with your GTXPoint credentials:
```env
GTXPOINT_PATH=https://paymentpage.eposservice.com
GTXPOINT_PROJECT_ID=your_project_id
GTXPOINT_SECRET_KEY=your_secret_key
```

### Running the Application

1. Start the Next.js development server:
```bash
npm run dev
```
The application will run on http://localhost:3000

2. Open your browser and navigate to http://localhost:3000

## Deployment to Vercel

### Option 1: Deploy via Vercel CLI

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel
   ```

4. **Set environment variables**
   ```bash
   vercel env add GTXPOINT_SECRET_KEY
   vercel env add GTXPOINT_PROJECT_ID
   vercel env add GTXPOINT_PATH
   ```

### Option 2: Deploy via Vercel Dashboard

1. **Connect your repository**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your Git repository

2. **Configure environment variables**
   In the Vercel dashboard, add these environment variables:
   - `GTXPOINT_PATH`: `https://paymentpage.eposservice.com`
   - `GTXPOINT_PROJECT_ID`: Your GTXPoint project ID
   - `GTXPOINT_SECRET_KEY`: Your GTXPoint secret key

3. **Deploy**
   - Click "Deploy"
   - Your app will be available at `https://your-app-name.vercel.app`

## Usage

1. **Configure Payment**: Fill out the payment configuration form with:
   - Payment Amount (required)
   - Payment Currency (required) 
   - Customer ID (required)
   - Optional customer details (email, name, phone, description)

2. **Generate Payment URL**: Click "Generate Payment URL" to create a secure payment link

3. **View Payment Page**: The payment page will be displayed in the iframe on the right side

4. **Manage URLs**: Use the toolbar to:
   - Copy the payment URL to clipboard
   - Toggle fullscreen mode
   - Open the payment page in a new tab

## API Endpoints

The Next.js API routes provide the following endpoints:

- `POST /api/payment/create` - Create a new payment URL
- `POST /api/payment/callback` - Handle payment callbacks from GTXPoint
- `GET /api/payment/status/[paymentId]` - Get payment status
- `GET /api/health` - Health check endpoint

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GTXPOINT_PATH` | GTXPoint payment page URL | Yes | `https://paymentpage.eposservice.com` |
| `GTXPOINT_PROJECT_ID` | Your GTXPoint project ID | Yes | `143693` |
| `GTXPOINT_SECRET_KEY` | Your GTXPoint secret key | Yes | (fallback provided) |
| `NEXT_PUBLIC_API_URL` | API base URL | No | Auto-detected |

## Configuration

### GTXPoint Configuration

The GTXPoint configuration is now managed via environment variables for better security. Set these in your `.env.local` file for development or in your Vercel dashboard for production.

### Supported Currencies

The application supports the following currencies:
- USD - US Dollar
- EUR - Euro  
- GBP - British Pound
- JPY - Japanese Yen
- CAD - Canadian Dollar
- AUD - Australian Dollar
- CHF - Swiss Franc
- CNY - Chinese Yuan

## Development

### Available Scripts

- `npm run dev` - Start Next.js development server
- `npm run build` - Build the Next.js application
- `npm run start` - Start the production Next.js server
- `npm run lint` - Run ESLint

### Building for Production

1. Build the Next.js application:
```bash
npm run build
```

2. Start the production server:
```bash
npm run start
```

## Security

- All payment processing is handled securely through GTXPoint
- The iframe uses sandbox attributes for security
- API calls include proper error handling
- Environment variables are used for configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.
#   e p o s _ s e r v i c e _ u i _ f o r m _ n e x t 
 
 # epos_service_ui_form_nextjs
