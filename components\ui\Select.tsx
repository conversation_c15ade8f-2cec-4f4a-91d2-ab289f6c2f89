'use client';

import { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  hint?: string;
  options: SelectOption[];
  placeholder?: string;
  variant?: 'default' | 'filled' | 'glass';
  inputSize?: 'sm' | 'md' | 'lg';
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ 
    className,
    label,
    error,
    hint,
    options,
    placeholder,
    variant = 'default',
    inputSize = 'md',
    disabled,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);

    const baseStyles = 'w-full transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed appearance-none cursor-pointer';
    
    const variants = {
      default: cn(
        'border-2 bg-white',
        error 
          ? 'border-error-500 focus:border-error-600 focus:ring-4 focus:ring-error-100' 
          : 'border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-100',
        'hover:border-neutral-400'
      ),
      filled: cn(
        'border-2 border-transparent bg-neutral-100',
        error
          ? 'focus:border-error-500 focus:ring-4 focus:ring-error-100 focus:bg-white'
          : 'focus:border-primary-500 focus:ring-4 focus:ring-primary-100 focus:bg-white',
        'hover:bg-neutral-200 focus:hover:bg-white'
      ),
      glass: cn(
        'border border-white/20 bg-white/10 backdrop-blur-md text-white',
        error
          ? 'focus:border-error-400 focus:ring-4 focus:ring-error-100/20'
          : 'focus:border-white/40 focus:ring-4 focus:ring-white/20',
        'hover:bg-white/20'
      ),
    };

    const sizes = {
      sm: 'px-3 py-2 text-sm rounded-lg',
      md: 'px-4 py-3 text-base rounded-xl',
      lg: 'px-5 py-4 text-lg rounded-xl',
    };

    return (
      <div className="space-y-2">
        {label && (
          <label className={cn(
            'block text-sm font-medium transition-colors',
            variant === 'glass' ? 'text-white' : 'text-neutral-700',
            error && 'text-error-600',
            isFocused && !error && 'text-primary-600'
          )}>
            {label}
          </label>
        )}
        
        <div className="relative">
          <select
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              'pr-10', // Space for dropdown arrow
              className
            )}
            disabled={disabled}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            ref={ref}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
                className={cn(
                  variant === 'glass' ? 'bg-neutral-800 text-white' : 'bg-white text-neutral-900'
                )}
              >
                {option.label}
              </option>
            ))}
          </select>
          
          {/* Custom dropdown arrow */}
          <div className={cn(
            'absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none',
            variant === 'glass' ? 'text-white/70' : 'text-neutral-400',
            error && 'text-error-500',
            isFocused && !error && 'text-primary-500'
          )}>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        
        {(error || hint) && (
          <div className="flex items-start gap-1">
            {error && (
              <svg className="w-4 h-4 text-error-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            <p className={cn(
              'text-sm',
              error ? 'text-error-600' : 'text-neutral-500'
            )}>
              {error || hint}
            </p>
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
