import { NextRequest, NextResponse } from 'next/server';
import { Callback } from 'gtxpoint';

export async function POST(request: NextRequest) {
  try {
    const callbackData = await request.json();
    
    // Get secret key from environment
    const secretKey = process.env.GTXPOINT_SECRET_KEY || 'default_secret_key';
    
    // Create GTXPoint callback instance
    const callback = new Callback(secretKey, callbackData);
    
    // Verify the callback
    const isSuccess = callback.isPaymentSuccess();
    const paymentId = callback.getPaymentId();
    
    // Log the callback for debugging
    console.log('Payment callback received:', {
      paymentId,
      isSuccess,
      callbackData: callback.callback
    });
    
    // Here you would typically:
    // 1. Update your database with the payment status
    // 2. Send confirmation emails
    // 3. Trigger any business logic
    
    const response = {
      success: true,
      paymentId,
      paymentStatus: isSuccess ? 'completed' : 'failed',
      message: `Payment ${paymentId} ${isSuccess ? 'completed successfully' : 'failed'}`,
      timestamp: new Date().toISOString()
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Callback processing error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process payment callback',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// Handle GET requests for testing
export async function GET() {
  return NextResponse.json({
    message: 'Payment callback endpoint is ready',
    method: 'POST',
    description: 'This endpoint receives payment callbacks from GTXPoint'
  });
}
