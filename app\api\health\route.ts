import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'EPOS Service UI Form',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      gtxpoint: {
        configured: !!(process.env.GTXPOINT_PATH && process.env.GTXPOINT_PROJECT_ID && process.env.GTXPOINT_SECRET_KEY),
        path: process.env.GTXPOINT_PATH || 'https://paymentpage.eposservice.com',
        projectId: process.env.GTXPOINT_PROJECT_ID || '143693'
      }
    };

    return NextResponse.json(health);
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
