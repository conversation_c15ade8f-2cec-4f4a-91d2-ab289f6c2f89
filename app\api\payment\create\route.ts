import { NextRequest, NextResponse } from 'next/server';
import { Payment } from 'gtxpoint';
import { PaymentConfig, PaymentResponse } from '@/types/payment';

export async function POST(request: NextRequest) {
  try {
    const body: PaymentConfig = await request.json();
    
    // Validate required fields
    if (!body.paymentAmount || !body.paymentCurrency || !body.customerId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: paymentAmount, paymentCurrency, customerId' 
        },
        { status: 400 }
      );
    }

    // Get environment variables
    const gtxpointPath = process.env.GTXPOINT_PATH || 'https://paymentpage.eposservice.com';
    const projectId = process.env.GTXPOINT_PROJECT_ID || '143693';
    const secretKey = process.env.GTXPOINT_SECRET_KEY || 'default_secret_key';

    // Create GTXPoint payment instance
    const payment = new Payment(gtxpointPath, projectId, secretKey);
    
    // Set required fields
    payment.paymentAmount = body.paymentAmount;
    payment.paymentCurrency = body.paymentCurrency;
    payment.customerId = body.customerId;
    
    // Set optional fields
    if (body.paymentId) payment.paymentId = body.paymentId;
    if (body.paymentDescription) payment.paymentDescription = body.paymentDescription;
    if (body.customerEmail) payment.customerEmail = body.customerEmail;
    if (body.customerFirstName) payment.customerFirstName = body.customerFirstName;
    if (body.customerLastName) payment.customerLastName = body.customerLastName;
    if (body.customerPhone) payment.customerPhone = body.customerPhone;
    if (body.merchantSuccessUrl) payment.merchantSuccessUrl = body.merchantSuccessUrl;
    if (body.merchantFailUrl) payment.merchantFailUrl = body.merchantFailUrl;

    // Generate payment URL
    const paymentUrl = payment.getUrl();

    const response: PaymentResponse = {
      success: true,
      paymentUrl,
      paymentId: payment.paymentId,
      amount: payment.paymentAmount,
      currency: payment.paymentCurrency,
      message: 'Payment URL generated successfully'
    };

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Payment creation error:', error);
    
    const errorResponse: PaymentResponse = {
      success: false,
      error: 'Failed to create payment URL',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
