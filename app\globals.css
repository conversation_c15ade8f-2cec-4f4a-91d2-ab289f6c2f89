@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    /* @apply border-border; */
  }

  body {
    /* @apply bg-background text-foreground; */
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-200 text-primary-900;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
}

@layer components {
  /* Glass morphism utility */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Gradient text utility */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Premium button hover effects */
  .btn-premium {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .btn-premium::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .btn-premium:hover::before {
    transform: translateX(100%);
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-strong hover:-translate-y-1;
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse glow effect */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
  }
}
