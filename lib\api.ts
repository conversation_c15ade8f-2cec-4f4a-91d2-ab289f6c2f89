import { PaymentConfig, PaymentResponse } from '@/types/payment';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

export async function createPaymentUrl(config: PaymentConfig): Promise<PaymentResponse> {
  try {
    const response = await fetch(`/api/payment/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: PaymentResponse = await response.json();
    return data;
  } catch (error) {
    console.error('API Error:', error);
    return {
      success: false,
      error: 'Failed to create payment URL',
      message: error instanceof Error ? error.message : 'Network error occurred'
    };
  }
}

export async function checkHealth(): Promise<any> {
  try {
    const response = await fetch(`/api/health`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Health check error:', error);
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export function generatePaymentId(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8);
  return `PAY_${timestamp}_${random}`.toUpperCase();
}
