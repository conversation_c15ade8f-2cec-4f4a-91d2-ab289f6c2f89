'use client';

import { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'glass';
  inputSize?: 'sm' | 'md' | 'lg';
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className,
    type = 'text',
    label,
    error,
    hint,
    leftIcon,
    rightIcon,
    variant = 'default',
    inputSize = 'md',
    disabled,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);

    const baseStyles = 'w-full transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed';
    
    const variants = {
      default: cn(
        'border-2 bg-white',
        error 
          ? 'border-error-500 focus:border-error-600 focus:ring-4 focus:ring-error-100' 
          : 'border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-100',
        'hover:border-neutral-400'
      ),
      filled: cn(
        'border-2 border-transparent bg-neutral-100',
        error
          ? 'focus:border-error-500 focus:ring-4 focus:ring-error-100 focus:bg-white'
          : 'focus:border-primary-500 focus:ring-4 focus:ring-primary-100 focus:bg-white',
        'hover:bg-neutral-200 focus:hover:bg-white'
      ),
      glass: cn(
        'border border-white/20 bg-white/10 backdrop-blur-md text-white placeholder:text-white/70',
        error
          ? 'focus:border-error-400 focus:ring-4 focus:ring-error-100/20'
          : 'focus:border-white/40 focus:ring-4 focus:ring-white/20',
        'hover:bg-white/20'
      ),
    };

    const sizes = {
      sm: 'px-3 py-2 text-sm rounded-lg',
      md: 'px-4 py-3 text-base rounded-xl',
      lg: 'px-5 py-4 text-lg rounded-xl',
    };

    const iconSizes = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    return (
      <div className="space-y-2">
        {label && (
          <label className={cn(
            'block text-sm font-medium transition-colors',
            variant === 'glass' ? 'text-white' : 'text-neutral-700',
            error && 'text-error-600',
            isFocused && !error && 'text-primary-600'
          )}>
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className={cn(
              'absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none',
              iconSizes[inputSize],
              variant === 'glass' ? 'text-white/70' : 'text-neutral-400',
              error && 'text-error-500',
              isFocused && !error && 'text-primary-500'
            )}>
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              className
            )}
            disabled={disabled}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className={cn(
              'absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none',
              iconSizes[inputSize],
              variant === 'glass' ? 'text-white/70' : 'text-neutral-400',
              error && 'text-error-500',
              isFocused && !error && 'text-primary-500'
            )}>
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || hint) && (
          <div className="flex items-start gap-1">
            {error && (
              <svg className="w-4 h-4 text-error-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            <p className={cn(
              'text-sm',
              error ? 'text-error-600' : 'text-neutral-500'
            )}>
              {error || hint}
            </p>
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
