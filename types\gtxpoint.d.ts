declare module 'gtxpoint' {
  export class Payment {
    constructor(path: string, projectId: string, secretKey: string);
    paymentAmount: number;
    paymentId: string;
    paymentCurrency: string;
    customerId: string;
    paymentDescription: string;
    customerEmail?: string;
    customerFirstName?: string;
    customerLastName?: string;
    customerPhone?: string;
    merchantSuccessUrl?: string;
    merchantFailUrl?: string;
    getUrl(): string;
  }

  export class Callback {
    constructor(secretKey: string, callbackData: any);
    callback: any;
    isPaymentSuccess(): boolean;
    getPaymentId(): string;
  }
}
