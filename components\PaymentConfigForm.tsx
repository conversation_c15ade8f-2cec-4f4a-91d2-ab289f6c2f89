'use client';

import { useState } from 'react';
import { PaymentConfig, CURRENCY_OPTIONS } from '@/types/payment';
import { createPaymentUrl, generatePaymentId } from '@/lib/api';
import Card, { CardHeader, CardContent, CardFooter } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Select from '@/components/ui/Select';
import { formatCurrency } from '@/lib/utils';

interface PaymentConfigFormProps {
  onPaymentUrlGenerated: (url: string) => void;
  onLoading: (loading: boolean) => void;
}

export default function PaymentConfigForm({ onPaymentUrlGenerated, onLoading }: PaymentConfigFormProps) {
  const [config, setConfig] = useState<PaymentConfig>({
    paymentAmount: 0,
    paymentCurrency: 'USD',
    customerId: '',
    paymentId: '',
    paymentDescription: '',
    customerEmail: '',
    customerFirstName: '',
    customerLastName: '',
    customerPhone: '',
    merchantSuccessUrl: '',
    merchantFailUrl: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!config.paymentAmount || config.paymentAmount <= 0) {
      newErrors.paymentAmount = 'Payment amount must be greater than 0';
    }

    if (!config.paymentCurrency) {
      newErrors.paymentCurrency = 'Payment currency is required';
    }

    if (!config.customerId.trim()) {
      newErrors.customerId = 'Customer ID is required';
    }

    if (config.customerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    onLoading(true);

    try {
      // Generate payment ID if not provided
      const paymentConfig = {
        ...config,
        paymentId: config.paymentId || generatePaymentId(),
      };

      const response = await createPaymentUrl(paymentConfig);

      if (response.success && response.paymentUrl) {
        onPaymentUrlGenerated(response.paymentUrl);
        setConfig(prev => ({ ...prev, paymentId: response.paymentId || prev.paymentId }));
      } else {
        setErrors({ submit: response.error || 'Failed to generate payment URL' });
      }
    } catch (error) {
      setErrors({ submit: 'An unexpected error occurred' });
    } finally {
      setIsSubmitting(false);
      onLoading(false);
    }
  };

  const handleInputChange = (field: keyof PaymentConfig, value: string | number) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Card variant="elevated" className="animate-fade-in-up">
      <CardHeader
        title="Payment Configuration"
        subtitle="Configure your payment details and generate a secure payment URL"
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        }
      />

      <form onSubmit={handleSubmit}>
        <CardContent>
          {/* Required Fields Section */}
          <div className="space-y-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-neutral-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-neutral-600 font-medium">Required Information</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Payment Amount *"
                type="number"
                step="0.01"
                min="0.01"
                value={config.paymentAmount || ''}
                onChange={(e) => handleInputChange('paymentAmount', parseFloat(e.target.value) || 0)}
                error={errors.paymentAmount}
                placeholder="0.00"
                leftIcon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                }
                hint={config.paymentAmount > 0 ? `Amount: ${formatCurrency(config.paymentAmount, config.paymentCurrency)}` : undefined}
              />

              <Select
                label="Currency *"
                value={config.paymentCurrency}
                onChange={(e) => handleInputChange('paymentCurrency', e.target.value)}
                error={errors.paymentCurrency}
                options={CURRENCY_OPTIONS}
              />
            </div>

            <Input
              label="Customer ID *"
              value={config.customerId}
              onChange={(e) => handleInputChange('customerId', e.target.value)}
              error={errors.customerId}
              placeholder="Enter unique customer identifier"
              leftIcon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              }
            />
          </div>

          {/* Optional Fields Section */}
          <div className="space-y-6 mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-neutral-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-neutral-600 font-medium">Optional Information</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Payment ID"
                value={config.paymentId}
                onChange={(e) => handleInputChange('paymentId', e.target.value)}
                placeholder="Auto-generated if empty"
                hint="Leave empty to auto-generate a unique ID"
                leftIcon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                  </svg>
                }
              />

              <Input
                label="Customer Email"
                type="email"
                value={config.customerEmail}
                onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                error={errors.customerEmail}
                placeholder="<EMAIL>"
                leftIcon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                }
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="First Name"
                value={config.customerFirstName}
                onChange={(e) => handleInputChange('customerFirstName', e.target.value)}
                placeholder="John"
                leftIcon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              />

              <Input
                label="Last Name"
                value={config.customerLastName}
                onChange={(e) => handleInputChange('customerLastName', e.target.value)}
                placeholder="Doe"
                leftIcon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              />
            </div>

            <Input
              label="Phone Number"
              type="tel"
              value={config.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              placeholder="+****************"
              leftIcon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              }
            />

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Payment Description
              </label>
              <textarea
                value={config.paymentDescription}
                onChange={(e) => handleInputChange('paymentDescription', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 border-2 border-neutral-300 rounded-xl focus:border-primary-500 focus:ring-4 focus:ring-primary-100 focus:outline-none transition-all duration-200 resize-none"
                placeholder="Describe what this payment is for..."
              />
            </div>
          </div>
        </CardContent>

        <CardFooter>
          {errors.submit && (
            <div className="w-full bg-error-50 border border-error-200 rounded-xl p-4 mb-4 animate-fade-in">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-error-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p className="text-error-700 text-sm font-medium">{errors.submit}</p>
              </div>
            </div>
          )}

          <Button
            type="submit"
            variant="gradient"
            size="lg"
            isLoading={isSubmitting}
            className="w-full"
            leftIcon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            }
          >
            {isSubmitting ? 'Generating Payment URL...' : 'Generate Payment URL'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
